# C/C++ build system timings
generate_cxx_metadata
  [gap of 30ms]
  create-invalidation-state 46ms
  [gap of 20ms]
generate_cxx_metadata completed in 96ms

# C/C++ build system timings
generate_cxx_metadata 38ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 21ms
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 46ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 38ms
  [gap of 12ms]
generate_cxx_metadata completed in 68ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 26ms]
  create-invalidation-state 45ms
  [gap of 16ms]
generate_cxx_metadata completed in 87ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 11ms]
  create-invalidation-state 21ms
  [gap of 11ms]
generate_cxx_metadata completed in 43ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 44ms
  [gap of 13ms]
generate_cxx_metadata completed in 85ms

