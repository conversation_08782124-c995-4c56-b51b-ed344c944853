"""
Migration to add email and OTP fields to users table
This migration will:
1. Add email column to users table
2. Add otp_code and otp_expiry columns for OTP storage
3. Make mobile_number nullable (for backward compatibility)
4. Add indexes for email field
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from config import Config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def upgrade():
    """Add email and OTP columns to users table"""
    
    # Create Flask app with minimal configuration
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize SQLAlchemy
    db = SQLAlchemy()
    db.init_app(app)
    
    with app.app_context():
        
        try:
            from sqlalchemy import text

            # Check if email column already exists
            with db.engine.connect() as conn:
                result = conn.execute(text("SHOW COLUMNS FROM users LIKE 'email'"))
                email_exists = result.fetchone() is not None

            # Check if otp_code column already exists
            with db.engine.connect() as conn:
                result = conn.execute(text("SHOW COLUMNS FROM users LIKE 'otp_code'"))
                otp_code_exists = result.fetchone() is not None

            # Check if otp_expiry column already exists
            with db.engine.connect() as conn:
                result = conn.execute(text("SHOW COLUMNS FROM users LIKE 'otp_expiry'"))
                otp_expiry_exists = result.fetchone() is not None

            if email_exists and otp_code_exists and otp_expiry_exists:
                logger.info("Migration already completed. All required columns exist.")
                return
            
            # Step 1: Add email column (if not exists)
            if not email_exists:
                logger.info("Adding email column to users table...")
                with db.engine.connect() as conn:
                    conn.execute(text("""
                        ALTER TABLE users
                        ADD COLUMN email VARCHAR(255) UNIQUE
                    """))
                    conn.commit()
            else:
                logger.info("Email column already exists.")

            # Step 2: Add otp_code column (if not exists)
            if not otp_code_exists:
                logger.info("Adding otp_code column to users table...")
                with db.engine.connect() as conn:
                    conn.execute(text("""
                        ALTER TABLE users
                        ADD COLUMN otp_code VARCHAR(6)
                    """))
                    conn.commit()
            else:
                logger.info("OTP code column already exists.")

            # Step 3: Add otp_expiry column (if not exists)
            if not otp_expiry_exists:
                logger.info("Adding otp_expiry column to users table...")
                with db.engine.connect() as conn:
                    conn.execute(text("""
                        ALTER TABLE users
                        ADD COLUMN otp_expiry DATETIME
                    """))
                    conn.commit()
            else:
                logger.info("OTP expiry column already exists.")

            # Step 4: Make mobile_number nullable for backward compatibility
            logger.info("Making mobile_number column nullable...")
            try:
                with db.engine.connect() as conn:
                    conn.execute(text("""
                        ALTER TABLE users
                        MODIFY COLUMN mobile_number VARCHAR(15) UNIQUE
                    """))
                    conn.commit()
            except Exception as e:
                logger.warning(f"Could not modify mobile_number column: {e}")

            # Step 5: Add index on email column (if email column was just created)
            if not email_exists:
                logger.info("Adding index on email column...")
                try:
                    with db.engine.connect() as conn:
                        conn.execute(text("""
                            CREATE INDEX idx_users_email ON users(email)
                        """))
                        conn.commit()
                except Exception as e:
                    logger.warning(f"Could not create email index: {e}")

            # Step 6: For existing users, create temporary email addresses based on mobile numbers
            # This is just for migration - users will need to update their email addresses
            if not email_exists:
                logger.info("Creating temporary email addresses for existing users...")
                with db.engine.connect() as conn:
                    conn.execute(text("""
                        UPDATE users
                        SET email = CONCAT(mobile_number, '@temp.example.com')
                        WHERE email IS NULL AND mobile_number IS NOT NULL
                    """))
                    conn.commit()

                # Step 7: Make email column NOT NULL after populating it
                logger.info("Making email column NOT NULL...")
                try:
                    with db.engine.connect() as conn:
                        conn.execute(text("""
                            ALTER TABLE users
                            MODIFY COLUMN email VARCHAR(255) NOT NULL UNIQUE
                        """))
                        conn.commit()
                except Exception as e:
                    logger.warning(f"Could not make email column NOT NULL: {e}")

            logger.info("Migration completed successfully!")
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            raise

if __name__ == "__main__":
    upgrade()
