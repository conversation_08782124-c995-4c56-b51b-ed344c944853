# C/C++ build system timings
generate_cxx_metadata
  [gap of 10ms]
  create-invalidation-state 71ms
  [gap of 19ms]
generate_cxx_metadata completed in 100ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 14ms
  [gap of 14ms]
generate_cxx_metadata completed in 33ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 34ms]
  create-invalidation-state 37ms
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 98ms

