# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 24ms
  [gap of 11ms]
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 75ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 26ms
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 59ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 30ms
  [gap of 16ms]
generate_cxx_metadata completed in 64ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 32ms]
  create-invalidation-state 40ms
  [gap of 15ms]
  write-metadata-json-to-file 22ms
generate_cxx_metadata completed in 110ms

