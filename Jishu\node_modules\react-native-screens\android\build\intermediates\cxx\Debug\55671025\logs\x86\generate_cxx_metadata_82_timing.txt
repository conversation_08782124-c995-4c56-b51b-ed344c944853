# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 35ms
  [gap of 13ms]
generate_cxx_metadata completed in 65ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 23ms
generate_cxx_metadata completed in 47ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 47ms]
  create-invalidation-state 63ms
  [gap of 32ms]
generate_cxx_metadata completed in 142ms

# C/C++ build system timings
generate_cxx_metadata 14ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 11ms]
  create-invalidation-state 34ms
  [gap of 11ms]
generate_cxx_metadata completed in 56ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 20ms
  [gap of 10ms]
generate_cxx_metadata completed in 46ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 33ms
  [gap of 11ms]
generate_cxx_metadata completed in 61ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 42ms
  [gap of 51ms]
generate_cxx_metadata completed in 121ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 35ms
  [gap of 14ms]
generate_cxx_metadata completed in 66ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 21ms
generate_cxx_metadata completed in 33ms

# C/C++ build system timings
generate_cxx_metadata 20ms

