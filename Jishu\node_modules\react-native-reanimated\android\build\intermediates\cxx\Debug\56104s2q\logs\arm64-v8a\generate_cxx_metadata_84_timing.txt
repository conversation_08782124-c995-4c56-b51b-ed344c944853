# C/C++ build system timings
generate_cxx_metadata
  [gap of 50ms]
  create-invalidation-state 150ms
  [gap of 78ms]
  write-metadata-json-to-file 34ms
generate_cxx_metadata completed in 312ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 45ms]
  create-invalidation-state 64ms
  [gap of 32ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 157ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 105ms]
  create-invalidation-state 198ms
  [gap of 54ms]
  write-metadata-json-to-file 52ms
generate_cxx_metadata completed in 410ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 83ms
  [gap of 38ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 149ms

