# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 30ms
  [gap of 11ms]
generate_cxx_metadata completed in 57ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 16ms
  [gap of 17ms]
generate_cxx_metadata completed in 50ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 36ms]
  create-invalidation-state 85ms
  [gap of 13ms]
generate_cxx_metadata completed in 134ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 16ms
generate_cxx_metadata completed in 21ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 40ms]
  create-invalidation-state 81ms
  [gap of 38ms]
generate_cxx_metadata completed in 159ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 23ms
  [gap of 14ms]
generate_cxx_metadata completed in 58ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 32ms
  [gap of 11ms]
generate_cxx_metadata completed in 61ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 34ms]
  create-invalidation-state 73ms
  [gap of 13ms]
generate_cxx_metadata completed in 120ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 18ms
generate_cxx_metadata completed in 40ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 15ms
  [gap of 23ms]
generate_cxx_metadata completed in 44ms

# C/C++ build system timings
generate_cxx_metadata 27ms

