# C/C++ build system timings
generate_cxx_metadata 24ms

# C/C++ build system timings
generate_cxx_metadata 24ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 36ms
  [gap of 13ms]
generate_cxx_metadata completed in 68ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 32ms]
  create-invalidation-state 62ms
  [gap of 24ms]
  write-metadata-json-to-file 25ms
generate_cxx_metadata completed in 145ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 40ms]
  create-invalidation-state 140ms
  [gap of 192ms]
generate_cxx_metadata completed in 372ms

