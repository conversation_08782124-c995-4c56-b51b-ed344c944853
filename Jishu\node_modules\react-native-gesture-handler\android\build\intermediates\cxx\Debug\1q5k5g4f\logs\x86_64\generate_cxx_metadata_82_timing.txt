# C/C++ build system timings
generate_cxx_metadata
  [gap of 30ms]
  create-invalidation-state 58ms
  [gap of 20ms]
generate_cxx_metadata completed in 108ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 38ms]
  create-invalidation-state 34ms
  [gap of 13ms]
generate_cxx_metadata completed in 85ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 23ms
generate_cxx_metadata completed in 41ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 11ms]
  create-invalidation-state 23ms
generate_cxx_metadata completed in 41ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 34ms
  [gap of 15ms]
generate_cxx_metadata completed in 69ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 45ms
  [gap of 14ms]
generate_cxx_metadata completed in 82ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 26ms
  [gap of 10ms]
generate_cxx_metadata completed in 55ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 35ms
  [gap of 16ms]
generate_cxx_metadata completed in 72ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 49ms
  [gap of 21ms]
generate_cxx_metadata completed in 93ms

