# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 34ms
  [gap of 11ms]
generate_cxx_metadata completed in 61ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 41ms
generate_cxx_metadata completed in 69ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 11ms]
  create-invalidation-state 22ms
generate_cxx_metadata completed in 40ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 12ms]
  create-invalidation-state 32ms
  [gap of 62ms]
  write-metadata-json-to-file 20ms
generate_cxx_metadata completed in 127ms

# C/C++ build system timings
generate_cxx_metadata 21ms

