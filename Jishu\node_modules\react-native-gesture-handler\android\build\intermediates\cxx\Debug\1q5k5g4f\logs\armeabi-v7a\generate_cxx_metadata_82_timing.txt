# C/C++ build system timings
generate_cxx_metadata
  [gap of 36ms]
  create-invalidation-state 69ms
  [gap of 24ms]
generate_cxx_metadata completed in 129ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 17ms
  [gap of 19ms]
generate_cxx_metadata completed in 54ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 21ms
  [gap of 10ms]
generate_cxx_metadata completed in 40ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 27ms]
  create-invalidation-state 55ms
  [gap of 25ms]
generate_cxx_metadata completed in 107ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 35ms
  [gap of 22ms]
generate_cxx_metadata completed in 76ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 10ms]
  create-invalidation-state 21ms
generate_cxx_metadata completed in 38ms

# C/C++ build system timings
generate_cxx_metadata 14ms

