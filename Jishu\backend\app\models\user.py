from app import db
from datetime import datetime, timezone, timedelta

class User(db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    mobile_number = db.Column(db.String(15), unique=True, nullable=True, index=True)  # Made nullable for migration
    email = db.Column(db.String(255), unique=True, nullable=False, index=True)  # New email field
    name = db.Column(db.String(100))
    icon = db.Column(db.String(255))  # URL or identifier for avatar (column name in DB is 'icon')
    role = db.Column(db.String(20), default='student')  # 'admin' or 'student'
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    last_login = db.Column(db.DateTime)
    is_profile_complete = db.Column(db.<PERSON>, default=False)  # Flag to check if username and avatar are set

    # OTP related fields
    otp_code = db.Column(db.String(6))  # Store the OTP code
    otp_expiry = db.Column(db.DateTime)  # OTP expiration time

    # No need to define relationships here as they are defined in the related models with backref

    def __repr__(self):
        return f'<User {self.email}>'

    @staticmethod
    def get_by_mobile(mobile_number):
        return User.query.filter_by(mobile_number=mobile_number).first()

    @staticmethod
    def get_by_email(email):
        return User.query.filter_by(email=email).first()

    def is_admin(self):
        return self.role == 'admin'

    def set_otp(self, otp_code, expiry_minutes=10):
        """Set OTP code with expiry time"""
        self.otp_code = otp_code
        self.otp_expiry = datetime.now(timezone.utc) + timedelta(minutes=expiry_minutes)

    def verify_otp(self, otp_code):
        """Verify if the provided OTP is valid and not expired"""
        if not self.otp_code or not self.otp_expiry:
            return False

        # Check if OTP has expired
        # Handle both timezone-aware and naive datetime objects
        current_time = datetime.now(timezone.utc)
        expiry_time = self.otp_expiry

        # If expiry_time is naive, make it timezone-aware (assume UTC)
        if expiry_time.tzinfo is None:
            expiry_time = expiry_time.replace(tzinfo=timezone.utc)

        if current_time > expiry_time:
            return False

        # Check if OTP matches
        return self.otp_code == otp_code

    def clear_otp(self):
        """Clear OTP after successful verification"""
        self.otp_code = None
        self.otp_expiry = None
