[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: armeabi-v7a", "file_": "D:\\Jishu\\jishu\\Jishu\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'D:\\Jishu\\jishu\\Jishu\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\1q5k5g4f\\armeabi-v7a\\android_gradle_build.json' was up-to-date", "file_": "D:\\Jishu\\jishu\\Jishu\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Jishu\\jishu\\Jishu\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]