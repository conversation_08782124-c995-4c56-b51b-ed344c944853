# C/C++ build system timings
generate_cxx_metadata
  [gap of 31ms]
  create-invalidation-state 76ms
  [gap of 67ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 196ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 31ms
  write-metadata-json-to-file 24ms
generate_cxx_metadata completed in 72ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 34ms]
  create-invalidation-state 30ms
  write-metadata-json-to-file 15ms
generate_cxx_metadata completed in 84ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 34ms]
  create-invalidation-state 44ms
  [gap of 12ms]
  write-metadata-json-to-file 18ms
generate_cxx_metadata completed in 109ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 20ms
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 54ms

# C/C++ build system timings
generate_cxx_metadata 17ms

