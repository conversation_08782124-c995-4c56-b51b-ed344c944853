# C/C++ build system timings
generate_cxx_metadata
  [gap of 39ms]
  create-invalidation-state 55ms
  [gap of 12ms]
  write-metadata-json-to-file 20ms
generate_cxx_metadata completed in 126ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 28ms
  [gap of 12ms]
  write-metadata-json-to-file 22ms
generate_cxx_metadata completed in 78ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 45ms]
  create-invalidation-state 38ms
  [gap of 14ms]
  write-metadata-json-to-file 26ms
generate_cxx_metadata completed in 123ms

