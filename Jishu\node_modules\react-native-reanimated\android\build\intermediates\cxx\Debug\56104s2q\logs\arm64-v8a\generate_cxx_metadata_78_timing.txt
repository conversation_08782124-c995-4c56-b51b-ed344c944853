# C/C++ build system timings
generate_cxx_metadata
  [gap of 43ms]
  create-invalidation-state 95ms
  [gap of 29ms]
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 179ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 43ms]
  create-invalidation-state 168ms
  [gap of 60ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 287ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 40ms]
  create-invalidation-state 86ms
  [gap of 34ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 181ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 138ms]
  create-invalidation-state 219ms
  [gap of 96ms]
  write-metadata-json-to-file 47ms
generate_cxx_metadata completed in 501ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 73ms]
  create-invalidation-state 132ms
  [gap of 53ms]
  write-metadata-json-to-file 28ms
generate_cxx_metadata completed in 288ms

