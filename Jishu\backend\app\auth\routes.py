from flask import request, jsonify
from flask_jwt_extended import create_access_token, create_refresh_token, jwt_required, get_jwt_identity
from app import db
from app.auth import bp
from app.models.user import User
from app.utils.email import generate_otp, send_otp_email, send_welcome_email
import re

def validate_email(email):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

@bp.route('/request-otp', methods=['POST'])
def request_otp():
    data = request.get_json() or {}

    if 'email' not in data:
        return jsonify({'error': 'Email address is required'}), 400

    email = data['email'].lower().strip()

    # Validate email format
    if not validate_email(email):
        return jsonify({'error': 'Please enter a valid email address'}), 400

    # Generate OTP
    otp = generate_otp()

    # Check if user exists, if not create a temporary record or handle in verify step
    user = User.get_by_email(email)

    if user:
        # Set OTP for existing user
        user.set_otp(otp)
        db.session.commit()
    else:
        # For new users, we'll create the user in the verify step
        # For now, we'll store the OTP temporarily (in production, use Redis)
        pass

    # Send OTP email
    email_sent = send_otp_email(email, otp)

    # Development fallback: if email sending fails, still allow OTP verification
    if not email_sent:
        print(f"Email sending failed. Using development fallback OTP for {email}: {otp}")
        # In development, we'll still return success but use a hardcoded OTP
        otp = '123456'  # Development fallback OTP

        # Store the OTP in the user record if user exists
        if user:
            user.set_otp(otp)
            db.session.commit()

    print(f"OTP for {email}: {otp}")  # For development debugging

    return jsonify({
        'message': 'OTP sent successfully to your email' if email_sent else 'OTP generated for development (check console)',
        'email': email,
        'otp': otp  # Remove this in production!
    })

@bp.route('/verify-otp', methods=['POST'])
def verify_otp_route():
    data = request.get_json() or {}

    if 'email' not in data or 'otp' not in data:
        return jsonify({'error': 'Email and OTP are required'}), 400

    email = data['email'].lower().strip()
    otp = data['otp']

    # Validate email format
    if not validate_email(email):
        return jsonify({'error': 'Please enter a valid email address'}), 400

    # Check if user exists
    user = User.get_by_email(email)
    is_new_user = False

    if user:
        # Verify OTP for existing user
        if not user.verify_otp(otp):
            return jsonify({'error': 'Invalid or expired OTP'}), 401

        # Clear OTP after successful verification
        user.clear_otp()
    else:
        # For new users, we need to verify against the hardcoded OTP for development
        # In production, you'd store OTPs in Redis or similar
        if otp != '123456':  # Development fallback
            return jsonify({'error': 'Invalid OTP'}), 401

        # Create new user
        is_first_user = User.query.count() == 0
        role = 'admin' if is_first_user else 'student'

        user = User(
            email=email,
            role=role,
            is_profile_complete=False
        )
        db.session.add(user)
        is_new_user = True

    # Update last login
    from datetime import datetime, timezone
    user.last_login = datetime.now(timezone.utc)
    db.session.commit()

    # Send welcome email for new users
    if is_new_user and user.name:
        send_welcome_email(email, user.name)

    # Generate tokens
    access_token = create_access_token(identity=user.id)
    refresh_token = create_refresh_token(identity=user.id)

    return jsonify({
        'access_token': access_token,
        'refresh_token': refresh_token,
        'user': {
            'id': user.id,
            'email': user.email,
            'mobile_number': user.mobile_number,  # Keep for backward compatibility
            'name': user.name,
            'avatar': user.icon,  # Return as 'avatar' for frontend compatibility
            'role': user.role,
            'is_profile_complete': user.is_profile_complete
        },
        'is_new_user': is_new_user
    })

@bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    current_user_id = get_jwt_identity()
    access_token = create_access_token(identity=current_user_id)

    return jsonify({'access_token': access_token})

@bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': 'User not found'}), 404

    return jsonify({
        'id': user.id,
        'mobile_number': user.mobile_number,
        'name': user.name,
        'avatar': user.icon,  # Return as 'avatar' for frontend compatibility
        'role': user.role,
        'is_profile_complete': user.is_profile_complete
    })

@bp.route('/update-profile', methods=['POST'])
@jwt_required()
def update_profile():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'error': 'User not found'}), 404

    data = request.get_json() or {}

    # Update user profile
    if 'name' in data:
        user.name = data['name']
    if 'avatar' in data:
        user.icon = data['avatar']  # Store in 'icon' field

    # Mark profile as complete if name and icon are set
    if user.name and user.icon:
        user.is_profile_complete = True

    db.session.commit()

    return jsonify({
        'id': user.id,
        'mobile_number': user.mobile_number,
        'name': user.name,
        'avatar': user.icon,  # Return as 'avatar' for frontend compatibility
        'role': user.role,
        'is_profile_complete': user.is_profile_complete
    })

@bp.route('/complete-profile', methods=['POST'])
def complete_profile():
    """Complete user profile with username and avatar"""
    data = request.get_json() or {}

    # Log the received data for debugging
    print(f"AUTH routes complete-profile received data: {data}")

    # Check if email is provided (prioritize email, fallback to mobile for backward compatibility)
    email = data.get('email')
    mobile_number = data.get('mobile_number')

    if not email and not mobile_number:
        return jsonify({'error': 'Email or mobile number is required'}), 400

    # Find the user by email or mobile number
    user = None
    if email:
        user = User.get_by_email(email)
    elif mobile_number:
        user = User.get_by_mobile(mobile_number)

    if not user:
        identifier = email or mobile_number
        print(f"User not found with identifier: {identifier}")
        return jsonify({'error': 'User not found'}), 404

    # Validate required fields
    if 'name' not in data or not data['name']:
        return jsonify({'error': 'Name is required'}), 400
    if 'avatar' not in data or not data['avatar']:
        return jsonify({'error': 'Avatar is required'}), 400

    # Update user profile
    user.name = data['name']

    # Handle avatar path
    avatar = data['avatar']

    # Validate that the avatar is one of the expected filenames
    valid_avatars = ['engg1.jpg', 'engg2.jpg', 'doc.jpg', 'index.png']
    if avatar not in valid_avatars:
        print(f"Warning: Unexpected avatar filename: {avatar}")

    # Store the filename directly - the frontend will handle the path resolution
    user.icon = avatar

    # Log the avatar being saved
    identifier = user.email or user.mobile_number
    print(f"Saving avatar: {avatar} for user {identifier}")
    user.is_profile_complete = True

    try:
        db.session.commit()
        print(f"User profile updated successfully: {user.id}, {user.name}, {user.icon}, is_profile_complete: {user.is_profile_complete}")

        # Send welcome email for new users
        if user.email and user.name:
            send_welcome_email(user.email, user.name)

    except Exception as e:
        db.session.rollback()
        print(f"Error updating user profile: {e}")
        return jsonify({'error': f'Database error: {str(e)}'}), 500

    # Create tokens
    access_token = create_access_token(identity=user.id)
    refresh_token = create_refresh_token(identity=user.id)

    response_data = {
        'success': True,
        'message': 'Profile completed successfully',
        'user': {
            'id': user.id,
            'email': user.email,
            'mobile_number': user.mobile_number,  # Keep for backward compatibility
            'name': user.name,
            'avatar': user.icon,  # Return as 'avatar' for frontend compatibility
            'role': user.role,
            'is_profile_complete': user.is_profile_complete
        },
        'access_token': access_token,
        'refresh_token': refresh_token
    }

    print(f"Sending response: {response_data}")
    return jsonify(response_data)
