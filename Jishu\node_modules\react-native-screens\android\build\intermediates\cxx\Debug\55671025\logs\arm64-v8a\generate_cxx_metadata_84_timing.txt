# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 21ms
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 60ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 21ms
  [gap of 16ms]
generate_cxx_metadata completed in 58ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 34ms]
  create-invalidation-state 44ms
  [gap of 23ms]
  write-metadata-json-to-file 23ms
generate_cxx_metadata completed in 125ms

