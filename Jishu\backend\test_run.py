#!/usr/bin/env python3

import traceback
import sys

try:
    print("Starting Flask app test...")
    from app import create_app
    print("App import successful")
    
    app = create_app()
    print("App created successfully")
    
    print("Starting Flask server on 0.0.0.0:5000...")
    app.run(debug=True, host='0.0.0.0', port=5000)
    
except Exception as e:
    print(f"Error occurred: {e}")
    traceback.print_exc()
    sys.exit(1)
