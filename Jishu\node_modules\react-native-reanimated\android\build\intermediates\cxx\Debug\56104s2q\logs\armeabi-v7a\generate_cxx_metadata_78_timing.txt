# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 44ms
  [gap of 20ms]
generate_cxx_metadata completed in 87ms

# C/C++ build system timings
generate_cxx_metadata 21ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 14ms
  [gap of 14ms]
generate_cxx_metadata completed in 28ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 33ms
  [gap of 16ms]
generate_cxx_metadata completed in 65ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 83ms
  [gap of 42ms]
  write-metadata-json-to-file 38ms
generate_cxx_metadata completed in 183ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 45ms
  [gap of 38ms]
generate_cxx_metadata completed in 111ms

